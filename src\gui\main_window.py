"""
Main GUI window for the file transfer application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from pathlib import Path
from typing import Optional, List
from src.core.server import FileTransferServer
from src.core.client import FileTransferClient
from src.utils.network_utils import NetworkUtils
from src.utils.file_utils import FileUtils
from src.utils.logger import get_logger


class MainWindow:
    """
    Main application window with tabbed interface for server and client modes.
    """
    
    def __init__(self):
        """Initialize the main window."""
        self.logger = get_logger()
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("File Transfer Application")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Application state
        self.server: Optional[FileTransferServer] = None
        self.client: Optional[FileTransferClient] = None
        self.server_thread: Optional[threading.Thread] = None
        
        # Setup GUI
        self._setup_styles()
        self._create_widgets()
        self._setup_callbacks()
        
        # Center window
        self._center_window()
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _setup_styles(self):
        """Setup custom styles for the application."""
        # Apply modern theme instead of custom styles
        from src.gui.theme import ModernTheme
        self.style = ModernTheme.apply_theme(self.root)
        
    def _create_widgets(self):
        """Create and layout GUI widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="File Transfer Application", style='Heading.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self._create_send_tab()
        self._create_receive_tab()
        self._create_settings_tab()
        
        # Status bar
        self._create_status_bar(main_frame)
    
    def _create_send_tab(self):
        """Create the send files tab."""
        send_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(send_frame, text="Send Files")
        
        # Configure grid
        send_frame.columnconfigure(1, weight=1)
        
        # Connection settings
        conn_frame = ttk.LabelFrame(send_frame, text="Connection Settings", padding="10")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        conn_frame.columnconfigure(1, weight=1)
        
        # Host
        ttk.Label(conn_frame, text="Host:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.host_var = tk.StringVar(value="localhost")
        host_entry = ttk.Entry(conn_frame, textvariable=self.host_var)
        host_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Port
        ttk.Label(conn_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(conn_frame, textvariable=self.port_var, width=10)
        port_entry.grid(row=0, column=3, sticky=tk.W)
        
        # Connect button
        self.connect_btn = ttk.Button(conn_frame, text="Connect", command=self._connect_to_server, style='ConnectAction.TButton')
        self.connect_btn.grid(row=0, column=4, padx=(10, 0))
        
        # Connection status
        self.connection_status = ttk.Label(conn_frame, text="Not connected", style='Status.TLabel')
        self.connection_status.grid(row=1, column=0, columnspan=5, sticky=tk.W, pady=(5, 0))
        
        # File selection
        file_frame = ttk.LabelFrame(send_frame, text="File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(1, weight=1)
        
        # File selection buttons
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(btn_frame, text="Add Files", command=self._add_files, style='PrimaryAction.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="Add Folder", command=self._add_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="Clear List", command=self._clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # File list
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Create treeview for file list
        self.file_tree = ttk.Treeview(list_frame, columns=('size', 'status'), show='tree headings')
        self.file_tree.heading('#0', text='File Name')
        self.file_tree.heading('size', text='Size')
        self.file_tree.heading('status', text='Status')
        
        self.file_tree.column('#0', width=300)
        self.file_tree.column('size', width=100)
        self.file_tree.column('status', width=100)
        
        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for file list
        file_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        file_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)
        
        # Transfer controls
        transfer_frame = ttk.Frame(send_frame)
        transfer_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        self.send_btn = ttk.Button(transfer_frame, text="Send Files", command=self._send_files, state='disabled', style='SuccessAction.TButton')
        self.send_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Progress bar
        self.send_progress = ttk.Progressbar(transfer_frame, mode='determinate')
        self.send_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # Progress label
        self.send_progress_label = ttk.Label(transfer_frame, text="Ready")
        self.send_progress_label.pack(side=tk.RIGHT)
        
        # Store selected files
        self.selected_files: List[str] = []
    
    def _create_receive_tab(self):
        """Create the receive files tab."""
        receive_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(receive_frame, text="Receive Files")
        
        # Configure grid
        receive_frame.columnconfigure(0, weight=1)
        receive_frame.rowconfigure(2, weight=1)
        
        # Server settings
        server_frame = ttk.LabelFrame(receive_frame, text="Server Settings", padding="10")
        server_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        server_frame.columnconfigure(1, weight=1)
        
        # Listen address
        ttk.Label(server_frame, text="Listen on:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.listen_host_var = tk.StringVar(value="0.0.0.0")
        host_combo = ttk.Combobox(server_frame, textvariable=self.listen_host_var, state='readonly')
        host_combo['values'] = ['0.0.0.0 (All interfaces)', 'localhost (Local only)'] + NetworkUtils.get_all_local_ips()
        host_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Port
        ttk.Label(server_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.listen_port_var = tk.StringVar(value="8888")
        port_entry = ttk.Entry(server_frame, textvariable=self.listen_port_var, width=10)
        port_entry.grid(row=0, column=3, sticky=tk.W)
        
        # Download directory
        ttk.Label(server_frame, text="Download to:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.download_dir_var = tk.StringVar(value=str(Path.home() / "Downloads" / "FileTransfer"))
        dir_entry = ttk.Entry(server_frame, textvariable=self.download_dir_var)
        dir_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        
        ttk.Button(server_frame, text="Browse", command=self._browse_download_dir).grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=(5, 0), pady=(5, 0))
        
        # Server controls
        control_frame = ttk.Frame(receive_frame)
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_server_btn = ttk.Button(control_frame, text="Start Server", command=self._start_server)
        self.start_server_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_server_btn = ttk.Button(control_frame, text="Stop Server", command=self._stop_server, state='disabled')
        self.stop_server_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Server status
        self.server_status = ttk.Label(control_frame, text="Server stopped", style='Status.TLabel')
        self.server_status.pack(side=tk.LEFT, padx=(10, 0))
        
        # Activity log
        log_frame = ttk.LabelFrame(receive_frame, text="Activity Log", padding="10")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # Create text widget for log
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, state='disabled')
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for log
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

    def _create_settings_tab(self):
        """Create the settings tab."""
        settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(settings_frame, text="Settings")

        # Network settings
        network_frame = ttk.LabelFrame(settings_frame, text="Network Settings", padding="10")
        network_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        network_frame.columnconfigure(1, weight=1)

        # Chunk size
        ttk.Label(network_frame, text="Chunk Size (KB):").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.chunk_size_var = tk.StringVar(value="8")
        chunk_entry = ttk.Entry(network_frame, textvariable=self.chunk_size_var, width=10)
        chunk_entry.grid(row=0, column=1, sticky=tk.W)

        # Connection timeout
        ttk.Label(network_frame, text="Connection Timeout (s):").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.timeout_var = tk.StringVar(value="10")
        timeout_entry = ttk.Entry(network_frame, textvariable=self.timeout_var, width=10)
        timeout_entry.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # Security settings
        security_frame = ttk.LabelFrame(settings_frame, text="Security Settings", padding="10")
        security_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Enable encryption
        self.encryption_var = tk.BooleanVar(value=False)
        encryption_check = ttk.Checkbutton(security_frame, text="Enable encryption", variable=self.encryption_var)
        encryption_check.grid(row=0, column=0, sticky=tk.W)

        # Verify checksums
        self.checksum_var = tk.BooleanVar(value=True)
        checksum_check = ttk.Checkbutton(security_frame, text="Verify file checksums", variable=self.checksum_var)
        checksum_check.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

        # Application info
        info_frame = ttk.LabelFrame(settings_frame, text="Application Info", padding="10")
        info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        info_text = """File Transfer Application v1.0.0

A secure, fast, and user-friendly file transfer application.

Features:
• TCP-based reliable file transfer
• Intuitive GUI with progress tracking
• Multiple file transfers
• Optional encryption
• Cross-platform compatibility

Developed by: Hamza Damra
Email: <EMAIL>"""

        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.grid(row=0, column=0, sticky=(tk.W, tk.N))

    def _create_status_bar(self, parent):
        """Create the status bar."""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)

        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready", style='Status.TLabel')
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Network info
        local_ip = NetworkUtils.get_local_ip()
        network_info = f"Local IP: {local_ip}"
        self.network_label = ttk.Label(status_frame, text=network_info, style='Status.TLabel')
        self.network_label.grid(row=0, column=1, sticky=tk.E)

    def _setup_callbacks(self):
        """Setup callbacks for server and client events."""
        # This will be implemented when we create the server and client instances
        pass

    def _center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _on_closing(self):
        """Handle window closing event."""
        if self.server:
            self._stop_server()
        if self.client and self.client.is_connected():
            self.client.disconnect()
        self.root.destroy()

    # File selection methods
    def _add_files(self):
        """Add files to the transfer list."""
        files = filedialog.askopenfilenames(
            title="Select files to transfer",
            filetypes=[("All files", "*.*")]
        )

        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                self._add_file_to_tree(file_path)

    def _add_folder(self):
        """Add all files from a folder to the transfer list."""
        folder_path = filedialog.askdirectory(title="Select folder to transfer")

        if folder_path:
            folder = Path(folder_path)
            for file_path in folder.rglob('*'):
                if file_path.is_file():
                    file_str = str(file_path)
                    if file_str not in self.selected_files:
                        self.selected_files.append(file_str)
                        self._add_file_to_tree(file_str)

    def _clear_files(self):
        """Clear the file list."""
        self.selected_files.clear()
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

    def _add_file_to_tree(self, file_path: str):
        """Add a file to the tree view."""
        file_info = FileUtils.get_file_info(file_path)
        if file_info:
            self.file_tree.insert('', 'end',
                                text=file_info['name'],
                                values=(file_info['size_human'], 'Ready'))

    def _browse_download_dir(self):
        """Browse for download directory."""
        directory = filedialog.askdirectory(
            title="Select download directory",
            initialdir=self.download_dir_var.get()
        )

        if directory:
            self.download_dir_var.set(directory)

    # Connection methods
    def _connect_to_server(self):
        """Connect to the file transfer server."""
        if self.client and self.client.is_connected():
            self.client.disconnect()
            self.connect_btn.config(text="Connect")
            self.connection_status.config(text="Not connected")
            self.send_btn.config(state='disabled')
            return

        try:
            host = self.host_var.get().strip()
            port = int(self.port_var.get().strip())

            if not NetworkUtils.is_valid_ip(host) and host != "localhost":
                messagebox.showerror("Error", "Invalid host address")
                return

            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Error", "Invalid port number (1-65535)")
                return

            # Create client and setup callbacks
            chunk_size = int(self.chunk_size_var.get()) * 1024  # Convert KB to bytes
            self.client = FileTransferClient(chunk_size=chunk_size)
            self._setup_client_callbacks()

            # Connect
            self.connection_status.config(text="Connecting...")
            self.root.update()

            if self.client.connect(host, port):
                self.connect_btn.config(text="Disconnect")
                self.connection_status.config(text=f"Connected to {host}:{port}")
                self.send_btn.config(state='normal')
                self._log_message(f"Connected to server {host}:{port}")
            else:
                self.connection_status.config(text="Connection failed")
                messagebox.showerror("Connection Error", f"Failed to connect to {host}:{port}")

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
        except Exception as e:
            self.connection_status.config(text="Connection failed")
            messagebox.showerror("Connection Error", f"Error: {e}")

    def _start_server(self):
        """Start the file transfer server."""
        try:
            host = self.listen_host_var.get().split()[0]  # Extract IP from combo text
            port = int(self.listen_port_var.get().strip())
            download_dir = self.download_dir_var.get().strip()

            if not NetworkUtils.is_valid_port(port):
                messagebox.showerror("Error", "Invalid port number (1-65535)")
                return

            # Check if port is available
            if not NetworkUtils.is_port_available(host if host != "0.0.0.0" else "localhost", port):
                messagebox.showerror("Error", f"Port {port} is already in use")
                return

            # Create download directory
            if not FileUtils.ensure_directory_exists(download_dir):
                messagebox.showerror("Error", f"Cannot create download directory: {download_dir}")
                return

            # Create server and setup callbacks
            chunk_size = int(self.chunk_size_var.get()) * 1024  # Convert KB to bytes
            self.server = FileTransferServer(
                host=host,
                port=port,
                download_dir=download_dir,
                chunk_size=chunk_size
            )
            self._setup_server_callbacks()

            # Start server in separate thread
            self.server_thread = threading.Thread(target=self.server.start, daemon=True)
            self.server_thread.start()

            # Update UI
            self.start_server_btn.config(state='disabled')
            self.stop_server_btn.config(state='normal')
            self.server_status.config(text=f"Server running on {host}:{port}")
            self._log_message(f"Server started on {host}:{port}")
            self._log_message(f"Download directory: {download_dir}")

        except ValueError:
            messagebox.showerror("Error", "Invalid port number")
        except Exception as e:
            messagebox.showerror("Server Error", f"Error starting server: {e}")

    def _stop_server(self):
        """Stop the file transfer server."""
        if self.server:
            self.server.stop()
            self.server = None
            self.server_thread = None

            # Update UI
            self.start_server_btn.config(state='normal')
            self.stop_server_btn.config(state='disabled')
            self.server_status.config(text="Server stopped")
            self._log_message("Server stopped")

    def _send_files(self):
        """Send selected files to the server."""
        if not self.client or not self.client.is_connected():
            messagebox.showerror("Error", "Not connected to server")
            return

        if not self.selected_files:
            messagebox.showerror("Error", "No files selected")
            return

        # Disable send button during transfer
        self.send_btn.config(state='disabled')

        # Start transfer in separate thread
        transfer_thread = threading.Thread(target=self._transfer_files_thread, daemon=True)
        transfer_thread.start()

    def _transfer_files_thread(self):
        """Transfer files in a separate thread."""
        try:
            total_files = len(self.selected_files)

            for i, file_path in enumerate(self.selected_files):
                # Update progress
                self.root.after(0, lambda: self.send_progress_label.config(text=f"Sending {i+1}/{total_files}"))

                # Send file
                success = self.client.send_file(file_path)

                # Update file status in tree
                file_name = Path(file_path).name
                for item in self.file_tree.get_children():
                    if self.file_tree.item(item, 'text') == file_name:
                        status = "Sent" if success else "Failed"
                        self.file_tree.set(item, 'status', status)
                        break

                if not success:
                    self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Failed to send {file_name}"))

            # Reset UI
            self.root.after(0, lambda: self.send_progress.config(value=0))
            self.root.after(0, lambda: self.send_progress_label.config(text="Transfer complete"))
            self.root.after(0, lambda: self.send_btn.config(state='normal'))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Transfer Error", f"Error during transfer: {e}"))
            self.root.after(0, lambda: self.send_btn.config(state='normal'))

    # Callback setup methods
    def _setup_server_callbacks(self):
        """Setup callbacks for server events."""
        if self.server:
            self.server.on_client_connected = self._on_client_connected
            self.server.on_client_disconnected = self._on_client_disconnected
            self.server.on_file_received = self._on_file_received
            self.server.on_transfer_progress = self._on_server_transfer_progress

    def _setup_client_callbacks(self):
        """Setup callbacks for client events."""
        if self.client:
            self.client.on_connected = self._on_connected
            self.client.on_disconnected = self._on_disconnected
            self.client.on_transfer_progress = self._on_client_transfer_progress
            self.client.on_transfer_complete = self._on_transfer_complete
            self.client.on_transfer_error = self._on_transfer_error

    # Server event callbacks
    def _on_client_connected(self, client_id: str, client_address: tuple):
        """Handle client connection."""
        message = f"Client connected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_client_disconnected(self, client_id: str):
        """Handle client disconnection."""
        message = f"Client disconnected: {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_file_received(self, filename: str, client_id: str):
        """Handle file received."""
        message = f"File received: {filename} from {client_id}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_server_transfer_progress(self, filename: str, progress: float, bytes_received: int, total_bytes: int, client_id: str):
        """Handle server transfer progress."""
        message = f"Receiving {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_received)}/{FileUtils.format_file_size(total_bytes)})"
        self.root.after(0, lambda: self._log_message(message, replace_last=True))

    # Client event callbacks
    def _on_connected(self, host: str, port: int):
        """Handle successful connection."""
        pass  # Already handled in _connect_to_server

    def _on_disconnected(self):
        """Handle disconnection."""
        self.root.after(0, lambda: self.connection_status.config(text="Disconnected"))
        self.root.after(0, lambda: self.connect_btn.config(text="Connect"))
        self.root.after(0, lambda: self.send_btn.config(state='disabled'))

    def _on_client_transfer_progress(self, filename: str, progress: float, bytes_sent: int, total_bytes: int):
        """Handle client transfer progress."""
        self.root.after(0, lambda: self.send_progress.config(value=progress))
        message = f"Sending {filename}: {progress:.1f}% ({FileUtils.format_file_size(bytes_sent)}/{FileUtils.format_file_size(total_bytes)})"
        self.root.after(0, lambda: self.send_progress_label.config(text=message))

    def _on_transfer_complete(self, filename: str, success: bool):
        """Handle transfer completion."""
        if success:
            message = f"Transfer completed: {filename}"
        else:
            message = f"Transfer failed: {filename}"
        self.root.after(0, lambda: self._log_message(message))

    def _on_transfer_error(self, filename: str, error: str):
        """Handle transfer error."""
        message = f"Transfer error for {filename}: {error}"
        self.root.after(0, lambda: self._log_message(message))

    # Utility methods
    def _log_message(self, message: str, replace_last: bool = False):
        """Add a message to the activity log."""
        self.log_text.config(state='normal')

        if replace_last:
            # Remove last line and add new one
            self.log_text.delete("end-2l", "end-1l")

        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def _update_status(self, message: str):
        """Update the status bar."""
        self.status_label.config(text=message)

    def run(self):
        """Start the GUI application."""
        self.logger.info("Starting GUI application")
        self._log_message("File Transfer Application started")
        self.root.mainloop()
